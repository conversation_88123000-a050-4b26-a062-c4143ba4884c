#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
专门优化的手写数字识别系统 - 针对大图片和数字1识别问题
主要改进：
1. 多尺度检测算法
2. 专门的数字1检测策略
3. 改进的轮廓筛选算法
4. 增强的图像预处理
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras import datasets, layers, models, callbacks
from tensorflow.keras.layers import Input

# 配置参数
MODEL_PATH = "optimized_digit_model.h5"
DISPLAY_SIZE = (1200, 800)  # 增大显示尺寸
OVERLAP_THRESHOLD = 0.25

def create_optimized_model():
    """创建专门优化的模型"""
    print("正在创建优化模型...")
    
    # 加载MNIST数据集
    (train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()
    train_images = train_images.reshape((60000, 28, 28, 1)) / 255.0
    test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0
    train_mean = np.mean(train_images)
    train_images -= train_mean
    test_images -= train_mean
    
    # 大幅增强数字1的样本
    def super_enhance_digit_1(images, labels):
        """超级增强数字1样本"""
        digit_1_mask = labels == 1
        digit_1_imgs = images[digit_1_mask]
        digit_1_lbls = labels[digit_1_mask]
        
        enhanced_images = []
        enhanced_labels = []
        
        # 创建多种变形的数字1
        transformations = [
            # 基础变形
            {'rotation_range': 25, 'width_shift_range': 0.3, 'height_shift_range': 0.25, 'zoom_range': 0.4, 'shear_range': 0.3},
            # 极端细长变形
            {'rotation_range': 15, 'width_shift_range': 0.4, 'height_shift_range': 0.2, 'zoom_range': [0.7, 1.3], 'shear_range': 0.4},
            # 倾斜变形
            {'rotation_range': 35, 'width_shift_range': 0.25, 'height_shift_range': 0.3, 'zoom_range': 0.35, 'shear_range': 0.25},
            # 位置偏移
            {'rotation_range': 20, 'width_shift_range': 0.5, 'height_shift_range': 0.4, 'zoom_range': 0.3, 'shear_range': 0.2},
        ]
        
        for transform_params in transformations:
            datagen = tf.keras.preprocessing.image.ImageDataGenerator(
                fill_mode='constant', cval=0.0, **transform_params
            )
            
            # 生成多批增强样本
            for _ in range(3):
                augmented = next(datagen.flow(digit_1_imgs, digit_1_lbls, 
                                            batch_size=len(digit_1_imgs), shuffle=False))
                enhanced_images.append(augmented[0])
                enhanced_labels.append(augmented[1])
        
        # 合并所有增强样本
        for aug_imgs, aug_lbls in zip(enhanced_images, enhanced_labels):
            images = np.concatenate([images, aug_imgs])
            labels = np.concatenate([labels, aug_lbls])
        
        return images, labels
    
    # 超级增强数字1
    train_images, train_labels = super_enhance_digit_1(train_images, train_labels)
    print(f"增强后训练集大小: {len(train_images)}")
    
    # 构建更深的模型
    model = models.Sequential([
        Input(shape=(28, 28, 1)),
        
        # 多尺度特征提取
        layers.Conv2D(32, (1, 7), activation='relu', padding='same'),  # 垂直长条特征
        layers.Conv2D(32, (7, 1), activation='relu', padding='same'),  # 水平长条特征
        layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
        layers.Conv2D(64, (5, 5), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        layers.Dropout(0.1),
        
        # 深度特征提取
        layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
        layers.Conv2D(128, (3, 3), activation='relu'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        layers.Dropout(0.2),
        
        # 更深层特征
        layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
        layers.Conv2D(256, (3, 3), activation='relu'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        layers.Dropout(0.25),
        
        # 全连接层
        layers.Flatten(),
        layers.Dense(1024, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.5),
        layers.Dense(512, activation='relu'),
        layers.Dropout(0.4),
        layers.Dense(256, activation='relu'),
        layers.Dropout(0.3),
        layers.Dense(10)
    ])
    
    # 自定义损失函数，极大增强数字1的权重
    class_weights = {i: 1.0 for i in range(10)}
    class_weights[1] = 5.0  # 数字1权重增加5倍
    
    # 编译模型
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.0008, weight_decay=1e-4)
    model.compile(
        optimizer=optimizer,
        loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),
        metrics=['accuracy']
    )
    
    # 训练模型
    if not os.path.exists(MODEL_PATH):
        print("开始训练优化模型...")
        
        # 强化数据增强
        datagen = tf.keras.preprocessing.image.ImageDataGenerator(
            rotation_range=20,
            width_shift_range=0.25,
            height_shift_range=0.25,
            zoom_range=0.3,
            shear_range=0.25,
            fill_mode='constant',
            cval=0.0
        )
        datagen.fit(train_images)
        
        # 回调函数
        callback_list = [
            callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.3, patience=2, min_lr=1e-7),
            callbacks.EarlyStopping(monitor='val_accuracy', patience=12, restore_best_weights=True),
            callbacks.ModelCheckpoint(MODEL_PATH, monitor='val_accuracy', save_best_only=True)
        ]
        
        # 训练
        model.fit(
            datagen.flow(train_images, train_labels, batch_size=32),
            epochs=25,
            validation_data=(test_images, test_labels),
            callbacks=callback_list,
            class_weight=class_weights
        )
        
        # 加载最佳模型
        if os.path.exists(MODEL_PATH):
            model = tf.keras.models.load_model(MODEL_PATH)
            test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=0)
            print(f'模型在测试集上的准确率: {test_acc:.4f}')
    else:
        print("加载已训练的模型...")
        model = tf.keras.models.load_model(MODEL_PATH)
    
    return model, train_mean

def multi_scale_digit_detection(gray_img):
    """多尺度数字检测算法"""
    img_h, img_w = gray_img.shape
    all_contours = []
    
    # 多种尺度的处理
    scales = [1.0, 0.8, 1.2]  # 不同缩放比例
    
    for scale in scales:
        if scale != 1.0:
            new_h, new_w = int(img_h * scale), int(img_w * scale)
            scaled_img = cv2.resize(gray_img, (new_w, new_h))
        else:
            scaled_img = gray_img.copy()
        
        # 多种阈值方法
        thresholded_images = []
        
        # 方法1: OTSU
        _, thresh1 = cv2.threshold(scaled_img, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        thresholded_images.append(thresh1)
        
        # 方法2: 自适应阈值
        blurred = cv2.GaussianBlur(scaled_img, (3, 3), 0)
        thresh2 = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                       cv2.THRESH_BINARY_INV, 11, 2)
        thresholded_images.append(thresh2)
        
        # 方法3: 多个固定阈值
        for threshold_val in [100, 120, 140, 160]:
            _, thresh_fixed = cv2.threshold(scaled_img, threshold_val, 255, cv2.THRESH_BINARY_INV)
            thresholded_images.append(thresh_fixed)
        
        # 处理每个阈值图像
        for thresh_img in thresholded_images:
            # 形态学操作 - 专门优化细长形状
            kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 3))
            kernel_open = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 2))
            
            # 闭运算连接断开的部分
            processed = cv2.morphologyEx(thresh_img, cv2.MORPH_CLOSE, kernel_close)
            # 开运算去除噪声
            processed = cv2.morphologyEx(processed, cv2.MORPH_OPEN, kernel_open)
            
            # 查找轮廓
            contours, _ = cv2.findContours(processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 如果是缩放图像，需要将轮廓坐标还原
            if scale != 1.0:
                scaled_contours = []
                for cnt in contours:
                    scaled_cnt = cnt / scale
                    scaled_contours.append(scaled_cnt.astype(np.int32))
                all_contours.extend(scaled_contours)
            else:
                all_contours.extend(contours)
    
    return all_contours

def is_digit_1_enhanced(contour, img_shape):
    """增强的数字1检测"""
    x, y, w, h = cv2.boundingRect(contour)
    img_h, img_w = img_shape
    
    # 基本特征
    aspect_ratio = h / w if w > 0 else 0
    area = cv2.contourArea(contour)
    rect_area = w * h
    fill_ratio = area / rect_area if rect_area > 0 else 0
    
    # 数字1的多重判断条件
    conditions = [
        aspect_ratio > 1.3,  # 高宽比
        0.0005 < area / (img_h * img_w) < 0.4,  # 面积范围
        fill_ratio > 0.15,  # 填充率
        w < img_w * 0.3,  # 宽度不能太大
        h > img_h * 0.05,  # 高度不能太小
    ]
    
    # 至少满足4个条件
    return sum(conditions) >= 4

def calculate_overlap(rect1, rect2):
    """计算重叠率"""
    x1, y1, w1, h1 = rect1
    x2, y2, w2, h2 = rect2
    
    x_left = max(x1, x2)
    y_top = max(y1, y2)
    x_right = min(x1 + w1, x2 + w2)
    y_bottom = min(y1 + h1, y2 + h2)
    
    if x_right < x_left or y_bottom < y_top:
        return 0.0
    
    intersection_area = (x_right - x_left) * (y_bottom - y_top)
    area1 = w1 * h1
    area2 = w2 * h2
    
    return intersection_area / min(area1, area2)

def load_and_process_images(directory, train_mean):
    """优化的图像处理函数"""
    images = []
    original_images = []
    processed_images = []
    filenames = []
    digit_rois = []
    valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp')

    if not os.path.exists(directory):
        print(f"错误：目录 '{directory}' 不存在！")
        return [], [], [], [], []

    try:
        all_files = os.listdir(directory)
    except UnicodeDecodeError:
        all_files = os.listdir(directory.encode('utf-8').decode('gbk', errors='ignore'))

    image_files = [f for f in all_files if f.lower().endswith(valid_extensions)]
    print(f"找到图片文件：{image_files}")

    for filename in image_files:
        img_path = os.path.join(directory, filename)

        try:
            img_data = np.fromfile(img_path, dtype=np.uint8)
            img = cv2.imdecode(img_data, cv2.IMREAD_COLOR)
            if img is None:
                continue
        except Exception as e:
            print(f"无法读取 {filename}：{str(e)}")
            continue

        print(f"处理图片 {filename}，原始尺寸：{img.shape}")

        # 保存原始图片（适应大图片）
        h, w = img.shape[:2]
        scale = min(DISPLAY_SIZE[0]/w, DISPLAY_SIZE[1]/h)
        new_w, new_h = int(w * scale), int(h * scale)
        resized_img = cv2.resize(img, (new_w, new_h))
        padded_img = np.zeros((DISPLAY_SIZE[1], DISPLAY_SIZE[0], 3), dtype=np.uint8)
        y_offset, x_offset = (DISPLAY_SIZE[1]-new_h)//2, (DISPLAY_SIZE[0]-new_w)//2
        padded_img[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized_img
        original_images.append(padded_img.copy())

        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 使用多尺度检测
        all_contours = multi_scale_digit_detection(gray)

        # 智能轮廓筛选
        img_h, img_w = gray.shape
        min_area = max(50, img_h * img_w * 0.0002)  # 动态最小面积
        max_area = img_h * img_w * 0.5

        print(f"图片尺寸：{img_w}x{img_h}，最小面积阈值：{min_area}")

        candidate_contours = []
        for cnt in all_contours:
            area = cv2.contourArea(cnt)
            x, y, w_cnt, h_cnt = cv2.boundingRect(cnt)

            # 基本有效性检查
            basic_valid = (min_area < area < max_area and
                          0.1 < w_cnt/h_cnt < 4.0 and
                          x >= 0 and y >= 0 and
                          x + w_cnt <= img_w and
                          y + h_cnt <= img_h)

            # 数字1特殊检测
            is_digit_1 = is_digit_1_enhanced(cnt, gray.shape)

            if basic_valid or is_digit_1:
                candidate_contours.append((x, y, w_cnt, h_cnt, area, is_digit_1))

        print(f"候选轮廓数量：{len(candidate_contours)}")

        # 智能去重（优先保留数字1）
        if candidate_contours:
            # 按数字1优先级和面积排序
            candidate_contours.sort(key=lambda c: (not c[5], -c[4]))

            unique_contours = []
            for cnt in candidate_contours:
                x, y, w_cnt, h_cnt, area, is_1 = cnt
                current_rect = (x, y, w_cnt, h_cnt)
                is_duplicate = False

                for unique_rect in unique_contours:
                    overlap = calculate_overlap(current_rect, unique_rect)
                    # 数字1使用更宽松的重叠阈值
                    threshold = 0.15 if is_1 else OVERLAP_THRESHOLD
                    if overlap > threshold:
                        is_duplicate = True
                        break

                if not is_duplicate:
                    unique_contours.append(current_rect)

            # 按x坐标排序
            unique_contours.sort(key=lambda c: c[0])
            digit_contours = unique_contours
        else:
            digit_contours = []

        print(f"最终检测到 {len(digit_contours)} 个数字")

        # 处理每个数字
        current_digits = []
        current_rois = []
        scale_x, scale_y = new_w / img_w, new_h / img_h

        for i, (x, y, w_cnt, h_cnt) in enumerate(digit_contours):
            # 绘制检测框
            x_scaled = int(x * scale_x) + x_offset
            y_scaled = int(y * scale_y) + y_offset
            w_scaled = int(w_cnt * scale_x)
            h_scaled = int(h_cnt * scale_y)

            padding = 3
            x_scaled = max(0, x_scaled - padding)
            y_scaled = max(0, y_scaled - padding)
            w_scaled = min(padded_img.shape[1] - x_scaled, w_scaled + 2 * padding)
            h_scaled = min(padded_img.shape[0] - y_scaled, h_scaled + 2 * padding)

            cv2.rectangle(padded_img, (x_scaled, y_scaled),
                         (x_scaled + w_scaled, y_scaled + h_scaled), (0, 255, 0), 3)
            cv2.putText(padded_img, f"{i+1}", (x_scaled, y_scaled - 8),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

            # 提取并预处理ROI
            padding_roi = 5
            x_roi = max(0, x - padding_roi)
            y_roi = max(0, y - padding_roi)
            w_roi = min(img_w - x_roi, w_cnt + 2 * padding_roi)
            h_roi = min(img_h - y_roi, h_cnt + 2 * padding_roi)

            roi_gray = gray[y_roi:y_roi+h_roi, x_roi:x_roi+w_roi]

            # 多步骤ROI处理
            # 1. 直方图均衡化
            roi_gray = cv2.equalizeHist(roi_gray)

            # 2. 高斯滤波去噪
            roi_gray = cv2.GaussianBlur(roi_gray, (3, 3), 0)

            # 3. 自适应阈值
            _, roi_thresh = cv2.threshold(roi_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

            # 4. 形态学操作优化
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
            roi_thresh = cv2.morphologyEx(roi_thresh, cv2.MORPH_CLOSE, kernel)

            # 调整为正方形
            roi_h, roi_w = roi_thresh.shape
            max_dim = max(roi_h, roi_w)
            square_roi = np.zeros((max_dim, max_dim), dtype=np.uint8)
            y_offset_roi = (max_dim - roi_h) // 2
            x_offset_roi = (max_dim - roi_w) // 2
            square_roi[y_offset_roi:y_offset_roi+roi_h, x_offset_roi:x_offset_roi+roi_w] = roi_thresh

            # 调整为28x28
            resized = cv2.resize(square_roi, (28, 28), interpolation=cv2.INTER_AREA)

            # 归一化
            normalized = resized / 255.0 - train_mean

            current_digits.append(normalized)
            current_rois.append(cv2.resize(square_roi, (120, 120), interpolation=cv2.INTER_AREA))

        processed_images.append(padded_img)

        if current_digits:
            images.append(current_digits)
            digit_rois.append(current_rois)
            filenames.append(filename)
            print(f"成功处理 {filename}，分割出 {len(current_digits)} 个数字")
        else:
            print(f"警告：在 {filename} 中未检测到数字")

    return images, original_images, processed_images, digit_rois, filenames

def predict_digits_enhanced(model, images):
    """增强的数字预测"""
    all_probabilities = []
    all_predictions = []
    all_confidences = []

    for digits in images:
        if not digits:
            all_probabilities.append([])
            all_predictions.append([])
            all_confidences.append([])
            continue

        input_data = np.array(digits).reshape(-1, 28, 28, 1)

        # 使用TTA (Test Time Augmentation) 提高准确性
        predictions_list = []

        # 原始预测
        logits = model.predict(input_data, verbose=0)
        predictions_list.append(logits)

        # 轻微旋转预测
        for angle in [-5, 5]:
            rotated_data = []
            for img in input_data:
                img_2d = img.squeeze()
                center = (14, 14)
                M = cv2.getRotationMatrix2D(center, angle, 1.0)
                rotated = cv2.warpAffine(img_2d, M, (28, 28))
                rotated_data.append(rotated.reshape(28, 28, 1))

            rotated_data = np.array(rotated_data)
            logits_rot = model.predict(rotated_data, verbose=0)
            predictions_list.append(logits_rot)

        # 平均预测结果
        avg_logits = np.mean(predictions_list, axis=0)

        # Softmax转换
        def softmax(x):
            x_max = np.max(x, axis=1, keepdims=True)
            e_x = np.exp(x - x_max)
            return e_x / e_x.sum(axis=1, keepdims=True)

        probabilities = softmax(avg_logits)
        predictions = np.argmax(probabilities, axis=1)
        confidences = np.max(probabilities, axis=1) * 100

        all_probabilities.append(probabilities)
        all_predictions.append(predictions)
        all_confidences.append(confidences)

    return all_probabilities, all_predictions, all_confidences

def visualize_enhanced_results(original_images, processed_images, digit_rois, filenames,
                              predictions, confidences, probabilities):
    """增强的结果可视化"""
    for i in range(len(filenames)):
        print(f"\n{'='*20} 图片 {filenames[i]} 分析结果 {'='*20}")

        # 显示原始图片和检测结果
        plt.figure(figsize=(18, 10))

        plt.subplot(2, 1, 1)
        plt.imshow(cv2.cvtColor(original_images[i], cv2.COLOR_BGR2RGB))
        plt.title(f"原始图片：{filenames[i]}", fontsize=14)
        plt.axis('off')

        plt.subplot(2, 1, 2)
        plt.imshow(cv2.cvtColor(processed_images[i], cv2.COLOR_BGR2RGB))
        plt.title(f"数字检测结果：共检测到 {len(predictions[i])} 个数字", fontsize=14)
        plt.axis('off')

        plt.tight_layout()
        plt.show()

        # 显示识别结果
        if len(predictions[i]) > 0:
            combined_result = ''.join(map(str, predictions[i]))
            avg_confidence = np.mean(confidences[i])
            print(f"\n🎯 最终识别结果：{combined_result}")
            print(f"📊 平均置信度：{avg_confidence:.1f}%")

            # 显示每个数字的详细信息
            cols = min(len(predictions[i]), 6)  # 最多6列
            rows = (len(predictions[i]) + cols - 1) // cols

            fig, axes = plt.subplots(rows, cols, figsize=(4*cols, 6*rows))
            if len(predictions[i]) == 1:
                axes = [axes]
            elif rows == 1:
                axes = [axes]
            else:
                axes = axes.flatten()

            for j in range(len(predictions[i])):
                if rows > 1 or cols > 1:
                    ax = axes[j]
                else:
                    ax = axes

                ax.imshow(digit_rois[i][j], cmap='gray')

                # 根据置信度设置颜色
                if confidences[i][j] >= 90:
                    color = 'green'
                elif confidences[i][j] >= 70:
                    color = 'orange'
                else:
                    color = 'red'

                ax.set_title(f"数字 {j+1}: {predictions[i][j]}\n置信度: {confidences[i][j]:.1f}%",
                           color=color, fontweight='bold')
                ax.axis('off')

            # 隐藏多余的子图
            for j in range(len(predictions[i]), len(axes)):
                if hasattr(axes[j], 'axis'):
                    axes[j].axis('off')

            plt.tight_layout()
            plt.show()

            # 详细分析每个数字
            print(f"\n📋 详细分析：")
            for j in range(len(predictions[i])):
                print(f"\n  数字 {j+1}:")
                print(f"    预测结果：{predictions[i][j]} (置信度：{confidences[i][j]:.1f}%)")

                # 显示前3个最可能的数字
                top3 = np.argsort(probabilities[i][j])[-3:][::-1]
                print(f"    前3个可能：")
                for k, digit in enumerate(top3):
                    prob = probabilities[i][j][digit] * 100
                    print(f"      {k+1}. 数字{digit}: {prob:.1f}%")

            print("\n" + "="*60)
        else:
            print("❌ 未检测到任何数字")

def main():
    """主程序"""
    print("🚀 启动优化的手写数字识别系统...")
    print("📚 正在准备优化模型...")

    model, train_mean = create_optimized_model()

    image_dir = "numbers"
    print(f"📁 处理目录：{image_dir}")

    images, original_images, processed_images, digit_rois, filenames = load_and_process_images(image_dir, train_mean)

    if len(images) > 0:
        print("🔍 开始预测...")
        probabilities, predicted_digits, confidence = predict_digits_enhanced(model, images)

        print("📊 显示结果...")
        visualize_enhanced_results(original_images, processed_images, digit_rois, filenames,
                                 predicted_digits, confidence, probabilities)
    else:
        print("❌ 没有找到可处理的图片文件")

if __name__ == "__main__":
    main()
