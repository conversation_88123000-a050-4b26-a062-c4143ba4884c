# 这是用于替换notebook中最后一个代码块的改进函数
# 将此代码复制到notebook的相应位置

# 配置参数（修改）
DISPLAY_SIZE = (600, 400)
OVERLAP_THRESHOLD = 0.4  # 降低重叠阈值，减少误删除

def load_and_preprocess_multidigit_images(directory, train_mean):
    """
    改进的图片预处理与数字分割函数
    主要改进：针对数字1的识别优化
    """
    images = []           # 预处理后的数字图像
    original_images = []  # 原始图像
    processed_images = [] # 框选后的图像
    filenames = []        # 文件名
    digit_rois = []       # 分割出的数字ROI
    valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp')
    
    if not os.path.exists(directory):
        print(f"错误：目录 '{directory}' 不存在！")
        return [], [], [], [], []
    
    try:
        all_files = os.listdir(directory)
    except UnicodeDecodeError:
        all_files = os.listdir(directory.encode('utf-8').decode('gbk', errors='ignore'))
    
    image_files = [f for f in all_files if f.lower().endswith(valid_extensions)]
    print(f"找到图片文件：{image_files}")

    for filename in image_files:
        img_path = os.path.join(directory, filename)
        
        try:
            # 读取图片（支持中文路径）
            img_data = np.fromfile(img_path, dtype=np.uint8)
            img = cv2.imdecode(img_data, cv2.IMREAD_COLOR)
            if img is None:
                print(f"警告：{filename} 无法解码为图像")
                continue
        except Exception as e:
            print(f"无法读取 {filename}：{str(e)}")
            continue
        
        # 保存原始图片（保持比例缩放）
        h, w = img.shape[:2]
        scale = min(DISPLAY_SIZE[0]/w, DISPLAY_SIZE[1]/h)
        new_w, new_h = int(w * scale), int(h * scale)
        resized_img = cv2.resize(img, (new_w, new_h))
        padded_img = np.zeros((DISPLAY_SIZE[1], DISPLAY_SIZE[0], 3), dtype=np.uint8)
        y_offset, x_offset = (DISPLAY_SIZE[1]-new_h)//2, (DISPLAY_SIZE[0]-new_w)//2
        padded_img[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized_img
        original_images.append(padded_img.copy())  # 保存原始图
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 多阈值处理策略（改进版）
        thresholds = []
        
        # 方法1：OTSU阈值
        _, thresh1 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        thresholds.append(thresh1)
        
        # 方法2：自适应阈值（适合光照不均）
        thresh2 = cv2.adaptiveThreshold(
            cv2.GaussianBlur(gray, (5, 5), 0), 255,
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
        )
        thresholds.append(thresh2)
        
        # 方法3：针对数字1的特殊阈值处理
        # 使用更小的核进行形态学操作，保留细线条
        kernel_small = np.ones((1, 1), np.uint8)
        thresh3 = cv2.morphologyEx(thresh1, cv2.MORPH_CLOSE, kernel_small)
        thresholds.append(thresh3)
        
        # 合并所有可能的轮廓
        all_contours = []
        for i, thresh in enumerate(thresholds):
            # 针对不同阈值使用不同的形态学操作
            if i == 2:  # 第三个阈值专门处理细线条
                kernel = np.ones((1, 2), np.uint8)
                thresh = cv2.dilate(thresh, kernel, iterations=1)
            else:
                kernel = np.ones((2, 2), np.uint8)
                thresh = cv2.erode(thresh, kernel, iterations=1)
                thresh = cv2.dilate(thresh, kernel, iterations=2)
            
            # 查找轮廓
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            all_contours.extend(contours)
        
        # 改进的轮廓筛选与去重（核心改进 - 针对数字1优化）
        digit_contours = []
        img_h, img_w = gray.shape
        
        # 动态调整面积阈值，特别针对细长的数字1
        base_min_area = max(15, img_h * img_w * 0.0002)  # 进一步降低最小面积阈值
        max_area = img_h * img_w * 0.5
        
        # 初步筛选轮廓
        candidate_contours = []
        for cnt in all_contours:
            area = cv2.contourArea(cnt)
            x, y, w_cnt, h_cnt = cv2.boundingRect(cnt)
            
            if h_cnt == 0 or w_cnt == 0:
                continue
                
            aspect_ratio = w_cnt / h_cnt
            
            # 针对数字1的特殊处理：允许更细长的比例和更小的面积
            is_digit_1_candidate = (aspect_ratio < 0.6 and h_cnt > w_cnt * 1.5)  # 细长形状
            is_very_thin = (aspect_ratio < 0.3 and h_cnt > w_cnt * 3)  # 非常细长
            
            # 动态调整阈值
            if is_very_thin:
                min_area = base_min_area * 0.3  # 非常细的数字1允许更小面积
                max_ratio = 5.0
            elif is_digit_1_candidate:
                min_area = base_min_area * 0.6  # 细长数字1允许较小面积
                max_ratio = 4.0
            else:
                min_area = base_min_area
                max_ratio = 2.5
            
            # 放宽筛选条件
            if (min_area < area < max_area and 
                0.1 < aspect_ratio < max_ratio and  # 进一步放宽比例限制
                x >= 0 and y >= 0 and 
                x + w_cnt <= img_w and 
                y + h_cnt <= img_h and
                h_cnt >= 8 and w_cnt >= 2):  # 降低最小尺寸要求
                
                candidate_contours.append((x, y, w_cnt, h_cnt, area, is_digit_1_candidate))
        
        # 改进的去重处理
        if candidate_contours:
            # 按面积降序排序，但对数字1候选者给予优先权
            def sort_key(c):
                x, y, w, h, area, is_digit_1 = c
                # 数字1候选者获得面积加权
                return area * (1.5 if is_digit_1 else 1.0)
            
            candidate_contours.sort(key=sort_key, reverse=True)
            
            # 去重处理
            unique_contours = []
            for cnt in candidate_contours:
                x, y, w_cnt, h_cnt, area, is_digit_1 = cnt
                current_rect = (x, y, w_cnt, h_cnt)
                is_duplicate = False
                
                # 与已保留的轮廓比较，检查是否重叠
                for unique_rect in unique_contours:
                    overlap = calculate_overlap(current_rect, unique_rect[:4])
                    # 对数字1候选者使用更严格的重叠阈值
                    threshold = OVERLAP_THRESHOLD * 0.7 if is_digit_1 else OVERLAP_THRESHOLD
                    if overlap > threshold:
                        is_duplicate = True
                        break
                
                if not is_duplicate:
                    unique_contours.append(current_rect + (is_digit_1,))
            
            # 按x坐标排序
            unique_contours.sort(key=lambda c: c[0])
            digit_contours = unique_contours
        
        # 处理每个数字轮廓
        current_digits = []
        current_rois = []
        scale_x, scale_y = new_w / img_w, new_h / img_h  # 缩放比例
        
        for i, contour_info in enumerate(digit_contours):
            x, y, w_cnt, h_cnt = contour_info[:4]
            is_digit_1 = contour_info[4] if len(contour_info) > 4 else False
            
            # 绘制框选（在展示图上）
            x_scaled = int(x * scale_x) + x_offset
            y_scaled = int(y * scale_y) + y_offset
            w_scaled = int(w_cnt * scale_x)
            h_scaled = int(h_cnt * scale_y)
            
            # 对数字1使用更小的padding
            padding = 2 if is_digit_1 else 3
            x_scaled = max(0, x_scaled - padding)
            y_scaled = max(0, y_scaled - padding)
            w_scaled = min(padded_img.shape[1] - x_scaled, w_scaled + 2 * padding)
            h_scaled = min(padded_img.shape[0] - y_scaled, h_scaled + 2 * padding)
            
            # 使用不同颜色标识数字1候选者
            color = (0, 255, 255) if is_digit_1 else (0, 255, 0)  # 黄色表示数字1候选
            cv2.rectangle(padded_img, (x_scaled, y_scaled), 
                         (x_scaled + w_scaled, y_scaled + h_scaled), color, 2)
            cv2.putText(padded_img, f"{i+1}{'*' if is_digit_1 else ''}", (x_scaled, y_scaled - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
            
            # 提取原始ROI
            x_original = max(0, x - padding)
            y_original = max(0, y - padding)
            w_original = min(img_w - x_original, w_cnt + 2 * padding)
            h_original = min(img_h - y_original, h_cnt + 2 * padding)
            
            # 对ROI单独处理，提高对比度
            roi_gray = gray[y_original:y_original+h_original, x_original:x_original+w_original]
            
            # 针对数字1使用不同的预处理策略
            if is_digit_1:
                # 对数字1使用更保守的处理，避免过度腐蚀
                roi_gray = cv2.equalizeHist(roi_gray)
                _, roi_thresh = cv2.threshold(roi_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
                # 轻微膨胀以连接断开的线条
                kernel = np.ones((2, 1), np.uint8)
                roi_thresh = cv2.dilate(roi_thresh, kernel, iterations=1)
            else:
                roi_gray = cv2.equalizeHist(roi_gray)
                _, roi_thresh = cv2.threshold(roi_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # 调整为正方形
            roi_h, roi_w = roi_thresh.shape
            max_dim = max(roi_h, roi_w)
            square_roi = np.zeros((max_dim, max_dim), dtype=np.uint8)
            y_offset_roi = (max_dim - roi_h) // 2
            x_offset_roi = (max_dim - roi_w) // 2
            square_roi[y_offset_roi:y_offset_roi+roi_h, x_offset_roi:x_offset_roi+roi_w] = roi_thresh
            
            # 调整为28x28
            resized = cv2.resize(square_roi, (28, 28), interpolation=cv2.INTER_AREA)
            
            # 归一化
            normalized = resized / 255.0 - train_mean
            
            current_digits.append(normalized)
            current_rois.append(cv2.resize(square_roi, (100, 100), interpolation=cv2.INTER_AREA))
        
        processed_images.append(padded_img)
        
        # 保存结果
        if current_digits:
            images.append(current_digits)
            digit_rois.append(current_rois)
            filenames.append(filename)
            print(f"成功处理 {filename}，分割出 {len(current_digits)} 个数字")
        else:
            # 兜底机制：整个图片作为一个数字处理
            print(f"警告：在 {filename} 中未检测到数字，尝试整体识别...")
            
            # 将整个图片作为一个数字处理
            gray_resized = cv2.resize(gray, (28, 28), interpolation=cv2.INTER_AREA)
            _, thresh = cv2.threshold(gray_resized, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            normalized = thresh / 255.0 - train_mean
            
            current_digits = [normalized]
            current_rois = [cv2.resize(thresh, (100, 100), interpolation=cv2.INTER_AREA)]
            
            images.append(current_digits)
            digit_rois.append(current_rois)
            filenames.append(filename)
            print(f"已将 {filename} 作为单个数字处理")

    return images, original_images, processed_images, digit_rois, filenames
