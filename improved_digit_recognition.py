#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
改进的手写数字识别系统 - 专门优化数字1的识别
主要改进：
1. 增强数字1的训练样本
2. 改进图像预处理，特别针对细长数字
3. 优化轮廓检测算法
4. 增加数字1的特征检测
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras import datasets, layers, models, callbacks
from tensorflow.keras.layers import Input

# 配置参数
MODEL_PATH = "improved_digit_model.h5"
DISPLAY_SIZE = (800, 600)
OVERLAP_THRESHOLD = 0.3

def create_enhanced_model():
    """创建增强的CNN模型，特别优化数字1的识别"""
    print("正在创建增强模型...")
    
    # 加载MNIST数据集
    (train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()
    train_images = train_images.reshape((60000, 28, 28, 1)) / 255.0
    test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0
    train_mean = np.mean(train_images)
    train_images -= train_mean
    test_images -= train_mean
    
    # 特别增强数字1的样本（增加更多变形）
    def enhance_digit_1(images, labels, multiply=5):
        """专门增强数字1的样本"""
        digit_1_mask = labels == 1
        digit_1_imgs = images[digit_1_mask]
        digit_1_lbls = labels[digit_1_mask]
        
        # 为数字1创建更多变形
        datagen = tf.keras.preprocessing.image.ImageDataGenerator(
            rotation_range=20,      # 增加旋转角度
            width_shift_range=0.25, # 增加水平偏移
            height_shift_range=0.2,
            zoom_range=0.3,         # 增加缩放范围
            shear_range=0.25,       # 增加剪切变形
            fill_mode='constant',
            cval=0.0
        )
        
        enhanced_images = []
        enhanced_labels = []
        
        for _ in range(multiply):
            augmented = next(datagen.flow(digit_1_imgs, digit_1_lbls, 
                                        batch_size=len(digit_1_imgs), shuffle=False))
            enhanced_images.append(augmented[0])
            enhanced_labels.append(augmented[1])
        
        # 合并所有增强的数字1样本
        for aug_imgs, aug_lbls in zip(enhanced_images, enhanced_labels):
            images = np.concatenate([images, aug_imgs])
            labels = np.concatenate([labels, aug_lbls])
        
        return images, labels
    
    # 增强数字1的样本
    train_images, train_labels = enhance_digit_1(train_images, train_labels, multiply=4)
    
    # 对其他易混淆数字也进行适度增强
    def augment_confusing_digits(images, labels, target_digits, multiply=2):
        for digit in target_digits:
            mask = labels == digit
            target_imgs = images[mask]
            target_lbls = labels[mask]
            
            datagen = tf.keras.preprocessing.image.ImageDataGenerator(
                rotation_range=15,
                width_shift_range=0.2,
                height_shift_range=0.2,
                zoom_range=0.25,
                shear_range=0.2,
                fill_mode='constant',
                cval=0.0
            )
            
            for _ in range(multiply-1):
                augmented = next(datagen.flow(target_imgs, target_lbls, 
                                            batch_size=len(target_imgs), shuffle=False))
                images = np.concatenate([images, augmented[0]])
                labels = np.concatenate([labels, augmented[1]])
        
        return images, labels
    
    # 增强容易与1混淆的数字：7, 4, 9
    train_images, train_labels = augment_confusing_digits(train_images, train_labels, [7, 4, 9], multiply=2)
    
    # 构建改进的模型架构
    model = models.Sequential([
        Input(shape=(28, 28, 1)),
        
        # 第一层：专门检测细长特征（有利于数字1）
        layers.Conv2D(32, (1, 5), activation='relu', padding='same'),  # 垂直特征
        layers.Conv2D(32, (5, 1), activation='relu', padding='same'),  # 水平特征
        layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        
        # 第二层：增强特征提取
        layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.Conv2D(128, (3, 3), activation='relu'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        
        # 第三层：深度特征
        layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.Dropout(0.25),
        layers.MaxPooling2D((2, 2)),
        
        # 全连接层
        layers.Flatten(),
        layers.Dense(512, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.5),
        layers.Dense(256, activation='relu'),
        layers.Dropout(0.3),
        layers.Dense(10)  # 10个数字类别
    ])
    
    # 使用自定义损失函数，增加数字1的权重
    class_weights = {i: 1.0 for i in range(10)}
    class_weights[1] = 2.0  # 数字1的权重加倍
    
    # 编译模型
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.001, weight_decay=1e-5)
    model.compile(
        optimizer=optimizer,
        loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),
        metrics=['accuracy']
    )
    
    # 训练模型
    if not os.path.exists(MODEL_PATH):
        print("开始训练增强模型...")
        
        # 数据增强
        datagen = tf.keras.preprocessing.image.ImageDataGenerator(
            rotation_range=12,
            width_shift_range=0.15,
            height_shift_range=0.15,
            zoom_range=0.2,
            shear_range=0.15,
            fill_mode='constant',
            cval=0.0
        )
        datagen.fit(train_images)
        
        # 回调函数
        callback_list = [
            callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=3),
            callbacks.EarlyStopping(monitor='val_accuracy', patience=10, restore_best_weights=True),
            callbacks.ModelCheckpoint(MODEL_PATH, monitor='val_accuracy', save_best_only=True)
        ]
        
        # 训练
        model.fit(
            datagen.flow(train_images, train_labels, batch_size=64),
            epochs=20,
            validation_data=(test_images, test_labels),
            callbacks=callback_list,
            class_weight=class_weights
        )
        
        # 加载最佳模型
        if os.path.exists(MODEL_PATH):
            model = tf.keras.models.load_model(MODEL_PATH)
            test_loss, test_acc = model.evaluate(test_images, test_labels, verbose=0)
            print(f'模型在测试集上的准确率: {test_acc:.4f}')
    else:
        print("加载已训练的模型...")
        model = tf.keras.models.load_model(MODEL_PATH)
    
    return model, train_mean

def improved_digit_detection(gray_img):
    """改进的数字检测算法，特别优化细长数字的检测"""
    img_h, img_w = gray_img.shape
    
    # 多种预处理方法
    processed_images = []
    
    # 方法1：标准OTSU
    _, thresh1 = cv2.threshold(gray_img, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    processed_images.append(thresh1)
    
    # 方法2：自适应阈值（对光照不均更鲁棒）
    thresh2 = cv2.adaptiveThreshold(
        cv2.GaussianBlur(gray_img, (3, 3), 0), 255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
    )
    processed_images.append(thresh2)
    
    # 方法3：针对细线条的处理
    kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 3))
    kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 1))
    
    # 增强垂直线条（有利于数字1）
    enhanced_vertical = cv2.morphologyEx(thresh1, cv2.MORPH_CLOSE, kernel_vertical)
    processed_images.append(enhanced_vertical)
    
    # 收集所有轮廓
    all_contours = []
    for processed in processed_images:
        # 轻微的形态学操作
        kernel = np.ones((2, 2), np.uint8)
        processed = cv2.morphologyEx(processed, cv2.MORPH_CLOSE, kernel)
        
        contours, _ = cv2.findContours(processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        all_contours.extend(contours)
    
    return all_contours

def calculate_overlap(rect1, rect2):
    """计算两个矩形的重叠率"""
    x1, y1, w1, h1 = rect1
    x2, y2, w2, h2 = rect2
    
    x_left = max(x1, x2)
    y_top = max(y1, y2)
    x_right = min(x1 + w1, x2 + w2)
    y_bottom = min(y1 + h1, y2 + h2)
    
    if x_right < x_left or y_bottom < y_top:
        return 0.0
    
    intersection_area = (x_right - x_left) * (y_bottom - y_top)
    area1 = w1 * h1
    area2 = w2 * h2
    
    return intersection_area / min(area1, area2)

def is_digit_1_like(contour, img_shape):
    """检测轮廓是否像数字1"""
    x, y, w, h = cv2.boundingRect(contour)
    img_h, img_w = img_shape

    # 数字1的特征：高宽比大，面积相对较小
    aspect_ratio = h / w if w > 0 else 0
    area = cv2.contourArea(contour)
    rect_area = w * h
    fill_ratio = area / rect_area if rect_area > 0 else 0

    # 数字1的判断条件（更宽松）
    is_tall_thin = aspect_ratio > 1.5  # 高宽比大于1.5
    is_reasonable_size = 0.001 < area / (img_h * img_w) < 0.3  # 合理的面积范围
    is_not_too_thick = fill_ratio > 0.2  # 不能太空

    return is_tall_thin and is_reasonable_size and is_not_too_thick

def load_and_preprocess_images(directory, train_mean):
    """改进的图像预处理函数"""
    images = []
    original_images = []
    processed_images = []
    filenames = []
    digit_rois = []
    valid_extensions = ('.jpg', '.jpeg', '.png', '.bmp')

    if not os.path.exists(directory):
        print(f"错误：目录 '{directory}' 不存在！")
        return [], [], [], [], []

    try:
        all_files = os.listdir(directory)
    except UnicodeDecodeError:
        all_files = os.listdir(directory.encode('utf-8').decode('gbk', errors='ignore'))

    image_files = [f for f in all_files if f.lower().endswith(valid_extensions)]
    print(f"找到图片文件：{image_files}")

    for filename in image_files:
        img_path = os.path.join(directory, filename)

        try:
            # 读取图片（支持中文路径）
            img_data = np.fromfile(img_path, dtype=np.uint8)
            img = cv2.imdecode(img_data, cv2.IMREAD_COLOR)
            if img is None:
                print(f"警告：{filename} 无法解码为图像")
                continue
        except Exception as e:
            print(f"无法读取 {filename}：{str(e)}")
            continue

        # 保存原始图片
        h, w = img.shape[:2]
        scale = min(DISPLAY_SIZE[0]/w, DISPLAY_SIZE[1]/h)
        new_w, new_h = int(w * scale), int(h * scale)
        resized_img = cv2.resize(img, (new_w, new_h))
        padded_img = np.zeros((DISPLAY_SIZE[1], DISPLAY_SIZE[0], 3), dtype=np.uint8)
        y_offset, x_offset = (DISPLAY_SIZE[1]-new_h)//2, (DISPLAY_SIZE[0]-new_w)//2
        padded_img[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized_img
        original_images.append(padded_img.copy())

        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 使用改进的数字检测
        all_contours = improved_digit_detection(gray)

        # 轮廓筛选与去重
        img_h, img_w = gray.shape
        min_area = max(20, img_h * img_w * 0.0003)  # 降低最小面积阈值
        max_area = img_h * img_w * 0.6

        candidate_contours = []
        for cnt in all_contours:
            area = cv2.contourArea(cnt)
            x, y, w_cnt, h_cnt = cv2.boundingRect(cnt)

            # 基本筛选条件
            basic_valid = (min_area < area < max_area and
                          0.15 < w_cnt/h_cnt < 3.0 and  # 放宽宽高比限制
                          x >= 0 and y >= 0 and
                          x + w_cnt <= img_w and
                          y + h_cnt <= img_h)

            # 特殊处理数字1
            is_digit_1 = is_digit_1_like(cnt, gray.shape)

            if basic_valid or is_digit_1:
                candidate_contours.append((x, y, w_cnt, h_cnt, area, is_digit_1))

        # 去重处理（优先保留数字1）
        if candidate_contours:
            # 按是否为数字1和面积排序
            candidate_contours.sort(key=lambda c: (not c[5], -c[4]))  # 数字1优先，然后按面积降序

            unique_contours = []
            for cnt in candidate_contours:
                x, y, w_cnt, h_cnt, area, is_1 = cnt
                current_rect = (x, y, w_cnt, h_cnt)
                is_duplicate = False

                for unique_rect in unique_contours:
                    overlap = calculate_overlap(current_rect, unique_rect[:4])
                    # 对数字1使用更低的重叠阈值
                    threshold = 0.2 if is_1 else OVERLAP_THRESHOLD
                    if overlap > threshold:
                        is_duplicate = True
                        break

                if not is_duplicate:
                    unique_contours.append(current_rect)

            # 按x坐标排序
            unique_contours.sort(key=lambda c: c[0])
            digit_contours = unique_contours
        else:
            digit_contours = []

        # 处理每个数字轮廓
        current_digits = []
        current_rois = []
        scale_x, scale_y = new_w / img_w, new_h / img_h

        for i, (x, y, w_cnt, h_cnt) in enumerate(digit_contours):
            # 绘制框选
            x_scaled = int(x * scale_x) + x_offset
            y_scaled = int(y * scale_y) + y_offset
            w_scaled = int(w_cnt * scale_x)
            h_scaled = int(h_cnt * scale_y)

            padding = 2
            x_scaled = max(0, x_scaled - padding)
            y_scaled = max(0, y_scaled - padding)
            w_scaled = min(padded_img.shape[1] - x_scaled, w_scaled + 2 * padding)
            h_scaled = min(padded_img.shape[0] - y_scaled, h_scaled + 2 * padding)

            cv2.rectangle(padded_img, (x_scaled, y_scaled),
                         (x_scaled + w_scaled, y_scaled + h_scaled), (0, 255, 0), 2)
            cv2.putText(padded_img, f"{i+1}", (x_scaled, y_scaled - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

            # 提取ROI并预处理
            padding_roi = 3
            x_roi = max(0, x - padding_roi)
            y_roi = max(0, y - padding_roi)
            w_roi = min(img_w - x_roi, w_cnt + 2 * padding_roi)
            h_roi = min(img_h - y_roi, h_cnt + 2 * padding_roi)

            roi_gray = gray[y_roi:y_roi+h_roi, x_roi:x_roi+w_roi]

            # 增强对比度
            roi_gray = cv2.equalizeHist(roi_gray)

            # 自适应阈值处理
            _, roi_thresh = cv2.threshold(roi_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

            # 调整为正方形
            roi_h, roi_w = roi_thresh.shape
            max_dim = max(roi_h, roi_w)
            square_roi = np.zeros((max_dim, max_dim), dtype=np.uint8)
            y_offset_roi = (max_dim - roi_h) // 2
            x_offset_roi = (max_dim - roi_w) // 2
            square_roi[y_offset_roi:y_offset_roi+roi_h, x_offset_roi:x_offset_roi+roi_w] = roi_thresh

            # 调整为28x28
            resized = cv2.resize(square_roi, (28, 28), interpolation=cv2.INTER_AREA)

            # 归一化
            normalized = resized / 255.0 - train_mean

            current_digits.append(normalized)
            current_rois.append(cv2.resize(square_roi, (100, 100), interpolation=cv2.INTER_AREA))

        processed_images.append(padded_img)

        if current_digits:
            images.append(current_digits)
            digit_rois.append(current_rois)
            filenames.append(filename)
            print(f"成功处理 {filename}，分割出 {len(current_digits)} 个数字")
        else:
            print(f"警告：在 {filename} 中未检测到数字")
            # 可以选择添加整体识别的兜底机制

    return images, original_images, processed_images, digit_rois, filenames

def predict_digits(model, images):
    """预测数字"""
    all_probabilities = []
    all_predictions = []
    all_confidences = []

    for digits in images:
        if not digits:
            all_probabilities.append([])
            all_predictions.append([])
            all_confidences.append([])
            continue

        input_data = np.array(digits).reshape(-1, 28, 28, 1)
        logits = model.predict(input_data, verbose=0)

        # Softmax转换
        def softmax(x):
            x_max = np.max(x, axis=1, keepdims=True)
            e_x = np.exp(x - x_max)
            return e_x / e_x.sum(axis=1, keepdims=True)

        probabilities = softmax(logits)
        predictions = np.argmax(probabilities, axis=1)
        confidences = np.max(probabilities, axis=1) * 100

        all_probabilities.append(probabilities)
        all_predictions.append(predictions)
        all_confidences.append(confidences)

    return all_probabilities, all_predictions, all_confidences

def visualize_results(original_images, processed_images, digit_rois, filenames,
                     predictions, confidences, probabilities):
    """可视化识别结果"""
    for i in range(len(filenames)):
        print(f"\n===== 图片 {filenames[i]} 分析结果 =====")

        # 显示原始图片和框选结果
        plt.figure(figsize=(15, 6))
        plt.subplot(2, 1, 1)
        plt.imshow(cv2.cvtColor(original_images[i], cv2.COLOR_BGR2RGB))
        plt.title(f"原始图片：{filenames[i]}")
        plt.axis('off')

        plt.subplot(2, 1, 2)
        plt.imshow(cv2.cvtColor(processed_images[i], cv2.COLOR_BGR2RGB))
        plt.title(f"数字检测结果：共检测到 {len(predictions[i])} 个数字")
        plt.axis('off')

        plt.tight_layout()
        plt.show()

        # 显示识别结果
        if len(predictions[i]) > 0:
            combined_result = ''.join(map(str, predictions[i]))
            print(f"组合识别结果：{combined_result}")

            # 显示每个数字的详细信息
            fig, axes = plt.subplots(1, len(predictions[i]), figsize=(3*len(predictions[i]), 5))
            if len(predictions[i]) == 1:
                axes = [axes]

            for j, ax in enumerate(axes):
                ax.imshow(digit_rois[i][j], cmap='gray')
                ax.set_title(f"预测：{predictions[i][j]}\n置信度：{confidences[i][j]:.1f}%")
                ax.axis('off')

            plt.tight_layout()
            plt.show()

            # 打印详细分析
            for j in range(len(predictions[i])):
                print(f"\n数字 {j+1} 分析：")
                print(f"预测结果：{predictions[i][j]}（置信度：{confidences[i][j]:.1f}%）")
                top3 = np.argsort(probabilities[i][j])[-3:][::-1]
                print(f"Top 3 可能的数字：")
                for k, digit in enumerate(top3):
                    print(f"  {k+1}. {digit}（{probabilities[i][j][digit]*100:.1f}%）")

            print("\n" + "-" * 60)

def main():
    """主程序"""
    print("正在准备增强模型...")
    model, train_mean = create_enhanced_model()

    image_dir = "numbers"  # 存放图片的文件夹
    images, original_images, processed_images, digit_rois, filenames = load_and_preprocess_images(image_dir, train_mean)

    if len(images) > 0:
        probabilities, predicted_digits, confidence = predict_digits(model, images)
        visualize_results(original_images, processed_images, digit_rois, filenames,
                         predicted_digits, confidence, probabilities)
    else:
        print("没有找到可处理的图片文件")

if __name__ == "__main__":
    main()
