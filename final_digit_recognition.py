#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终优化版本 - 专门解决数字1识别问题的手写数字识别系统
核心改进：
1. 专门的数字1检测算法
2. 多阈值融合检测
3. 智能轮廓筛选
4. 增强的模型训练
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow.keras import datasets, layers, models, callbacks
from tensorflow.keras.layers import Input

# 配置
MODEL_PATH = "final_digit_model.h5"
DISPLAY_SIZE = (1000, 700)

def create_final_model():
    """创建最终优化模型"""
    print("创建专门优化数字1识别的模型...")
    
    # 加载数据
    (train_images, train_labels), (test_images, test_labels) = datasets.mnist.load_data()
    train_images = train_images.reshape((60000, 28, 28, 1)) / 255.0
    test_images = test_images.reshape((10000, 28, 28, 1)) / 255.0
    train_mean = np.mean(train_images)
    train_images -= train_mean
    test_images -= train_mean
    
    # 大量增强数字1样本
    digit_1_mask = train_labels == 1
    digit_1_imgs = train_images[digit_1_mask]
    digit_1_lbls = train_labels[digit_1_mask]
    
    # 创建多种数字1变形
    enhanced_1_imgs = []
    enhanced_1_lbls = []
    
    # 多种数据增强策略
    augmentation_configs = [
        {'rotation_range': 30, 'width_shift_range': 0.4, 'height_shift_range': 0.3, 'zoom_range': 0.4, 'shear_range': 0.4},
        {'rotation_range': 20, 'width_shift_range': 0.5, 'height_shift_range': 0.2, 'zoom_range': [0.6, 1.4], 'shear_range': 0.3},
        {'rotation_range': 25, 'width_shift_range': 0.3, 'height_shift_range': 0.4, 'zoom_range': 0.5, 'shear_range': 0.5},
    ]
    
    for config in augmentation_configs:
        datagen = tf.keras.preprocessing.image.ImageDataGenerator(
            fill_mode='constant', cval=0.0, **config
        )
        
        # 生成增强样本
        for _ in range(4):  # 每种配置生成4批
            augmented = next(datagen.flow(digit_1_imgs, digit_1_lbls, 
                                        batch_size=len(digit_1_imgs), shuffle=False))
            enhanced_1_imgs.append(augmented[0])
            enhanced_1_lbls.append(augmented[1])
    
    # 合并增强样本
    for aug_imgs, aug_lbls in zip(enhanced_1_imgs, enhanced_1_lbls):
        train_images = np.concatenate([train_images, aug_imgs])
        train_labels = np.concatenate([train_labels, aug_lbls])
    
    print(f"增强后训练集大小: {len(train_images)} (数字1样本大幅增加)")
    
    # 构建模型
    model = models.Sequential([
        Input(shape=(28, 28, 1)),
        
        # 专门检测细长特征的卷积层
        layers.Conv2D(32, (1, 5), activation='relu', padding='same'),  # 垂直特征
        layers.Conv2D(32, (5, 1), activation='relu', padding='same'),  # 水平特征
        layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        
        layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
        layers.Conv2D(128, (3, 3), activation='relu'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        
        layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        
        layers.Flatten(),
        layers.Dense(512, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.5),
        layers.Dense(256, activation='relu'),
        layers.Dropout(0.3),
        layers.Dense(10)
    ])
    
    # 数字1权重增强
    class_weights = {i: 1.0 for i in range(10)}
    class_weights[1] = 3.0  # 数字1权重3倍
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
        loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),
        metrics=['accuracy']
    )
    
    # 训练模型
    if not os.path.exists(MODEL_PATH):
        print("开始训练模型...")
        
        datagen = tf.keras.preprocessing.image.ImageDataGenerator(
            rotation_range=15,
            width_shift_range=0.2,
            height_shift_range=0.2,
            zoom_range=0.2,
            shear_range=0.2,
            fill_mode='constant',
            cval=0.0
        )
        datagen.fit(train_images)
        
        callbacks_list = [
            callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=3),
            callbacks.EarlyStopping(monitor='val_accuracy', patience=8, restore_best_weights=True),
            callbacks.ModelCheckpoint(MODEL_PATH, monitor='val_accuracy', save_best_only=True)
        ]
        
        model.fit(
            datagen.flow(train_images, train_labels, batch_size=64),
            epochs=20,
            validation_data=(test_images, test_labels),
            callbacks=callbacks_list,
            class_weight=class_weights
        )
        
        if os.path.exists(MODEL_PATH):
            model = tf.keras.models.load_model(MODEL_PATH)
    else:
        print("加载已训练模型...")
        model = tf.keras.models.load_model(MODEL_PATH)
    
    return model, train_mean

def detect_digits_optimized(gray_img):
    """优化的数字检测算法"""
    img_h, img_w = gray_img.shape
    all_contours = []
    
    # 多种阈值方法
    thresholded_images = []
    
    # 1. OTSU阈值
    _, thresh1 = cv2.threshold(gray_img, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    thresholded_images.append(thresh1)
    
    # 2. 自适应阈值
    blurred = cv2.GaussianBlur(gray_img, (5, 5), 0)
    thresh2 = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                   cv2.THRESH_BINARY_INV, 11, 2)
    thresholded_images.append(thresh2)
    
    # 3. 多个固定阈值（适应不同光照）
    for threshold_val in [80, 100, 120, 140, 160, 180]:
        _, thresh_fixed = cv2.threshold(gray_img, threshold_val, 255, cv2.THRESH_BINARY_INV)
        thresholded_images.append(thresh_fixed)
    
    # 处理每个阈值图像
    for thresh_img in thresholded_images:
        # 形态学操作 - 专门优化细长形状
        kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 3))
        kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 1))
        
        # 增强垂直连接（有利于数字1）
        enhanced = cv2.morphologyEx(thresh_img, cv2.MORPH_CLOSE, kernel_vertical)
        # 去除水平噪声
        enhanced = cv2.morphologyEx(enhanced, cv2.MORPH_OPEN, kernel_horizontal)
        
        # 查找轮廓
        contours, _ = cv2.findContours(enhanced, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        all_contours.extend(contours)
    
    return all_contours

def is_likely_digit_1(contour, img_shape):
    """判断轮廓是否可能是数字1"""
    x, y, w, h = cv2.boundingRect(contour)
    img_h, img_w = img_shape
    
    # 计算特征
    aspect_ratio = h / w if w > 0 else 0
    area = cv2.contourArea(contour)
    rect_area = w * h
    fill_ratio = area / rect_area if rect_area > 0 else 0
    relative_area = area / (img_h * img_w)
    
    # 数字1的特征判断
    conditions = [
        aspect_ratio > 1.2,  # 高宽比大于1.2
        0.0003 < relative_area < 0.3,  # 相对面积合理
        fill_ratio > 0.1,  # 填充率不能太低
        w < img_w * 0.25,  # 宽度不能太大
        h > img_h * 0.03,  # 高度不能太小
    ]
    
    return sum(conditions) >= 4  # 至少满足4个条件

def calculate_overlap(rect1, rect2):
    """计算重叠率"""
    x1, y1, w1, h1 = rect1
    x2, y2, w2, h2 = rect2
    
    x_left = max(x1, x2)
    y_top = max(y1, y2)
    x_right = min(x1 + w1, x2 + w2)
    y_bottom = min(y1 + h1, y2 + h2)
    
    if x_right < x_left or y_bottom < y_top:
        return 0.0
    
    intersection_area = (x_right - x_left) * (y_bottom - y_top)
    area1 = w1 * h1
    area2 = w2 * h2
    
    return intersection_area / min(area1, area2)

def process_images(directory, train_mean):
    """处理图像"""
    images = []
    original_images = []
    processed_images = []
    filenames = []
    digit_rois = []
    
    if not os.path.exists(directory):
        print(f"目录 '{directory}' 不存在！")
        return [], [], [], [], []
    
    image_files = [f for f in os.listdir(directory) 
                   if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
    print(f"找到图片：{image_files}")

    for filename in image_files:
        img_path = os.path.join(directory, filename)
        
        try:
            img_data = np.fromfile(img_path, dtype=np.uint8)
            img = cv2.imdecode(img_data, cv2.IMREAD_COLOR)
            if img is None:
                continue
        except Exception as e:
            print(f"读取失败 {filename}: {e}")
            continue
        
        print(f"处理 {filename}, 尺寸: {img.shape}")
        
        # 保存原图
        h, w = img.shape[:2]
        scale = min(DISPLAY_SIZE[0]/w, DISPLAY_SIZE[1]/h)
        new_w, new_h = int(w * scale), int(h * scale)
        resized_img = cv2.resize(img, (new_w, new_h))
        padded_img = np.zeros((DISPLAY_SIZE[1], DISPLAY_SIZE[0], 3), dtype=np.uint8)
        y_offset, x_offset = (DISPLAY_SIZE[1]-new_h)//2, (DISPLAY_SIZE[0]-new_w)//2
        padded_img[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized_img
        original_images.append(padded_img.copy())
        
        # 转灰度
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 检测数字
        all_contours = detect_digits_optimized(gray)
        
        # 筛选轮廓
        img_h, img_w = gray.shape
        min_area = max(30, img_h * img_w * 0.0001)
        max_area = img_h * img_w * 0.4
        
        candidates = []
        for cnt in all_contours:
            area = cv2.contourArea(cnt)
            x, y, w_cnt, h_cnt = cv2.boundingRect(cnt)
            
            # 基本筛选
            basic_valid = (min_area < area < max_area and 
                          0.08 < w_cnt/h_cnt < 5.0 and
                          x >= 0 and y >= 0 and 
                          x + w_cnt <= img_w and 
                          y + h_cnt <= img_h)
            
            # 数字1特殊检测
            is_digit_1 = is_likely_digit_1(cnt, gray.shape)
            
            if basic_valid or is_digit_1:
                candidates.append((x, y, w_cnt, h_cnt, area, is_digit_1))
        
        # 去重（优先数字1）
        if candidates:
            candidates.sort(key=lambda c: (not c[5], -c[4]))  # 数字1优先，面积降序
            
            unique_rects = []
            for x, y, w_cnt, h_cnt, area, is_1 in candidates:
                current_rect = (x, y, w_cnt, h_cnt)
                is_duplicate = False
                
                for unique_rect in unique_rects:
                    overlap = calculate_overlap(current_rect, unique_rect)
                    threshold = 0.1 if is_1 else 0.3  # 数字1更宽松
                    if overlap > threshold:
                        is_duplicate = True
                        break
                
                if not is_duplicate:
                    unique_rects.append(current_rect)
            
            unique_rects.sort(key=lambda c: c[0])  # 按x坐标排序
        else:
            unique_rects = []
        
        print(f"检测到 {len(unique_rects)} 个数字")
        
        # 处理每个数字
        current_digits = []
        current_rois = []
        scale_x, scale_y = new_w / img_w, new_h / img_h
        
        for i, (x, y, w_cnt, h_cnt) in enumerate(unique_rects):
            # 绘制框
            x_scaled = int(x * scale_x) + x_offset
            y_scaled = int(y * scale_y) + y_offset
            w_scaled = int(w_cnt * scale_x)
            h_scaled = int(h_cnt * scale_y)
            
            cv2.rectangle(padded_img, (x_scaled, y_scaled), 
                         (x_scaled + w_scaled, y_scaled + h_scaled), (0, 255, 0), 2)
            cv2.putText(padded_img, f"{i+1}", (x_scaled, y_scaled - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # 提取ROI
            padding = 4
            x_roi = max(0, x - padding)
            y_roi = max(0, y - padding)
            w_roi = min(img_w - x_roi, w_cnt + 2 * padding)
            h_roi = min(img_h - y_roi, h_cnt + 2 * padding)
            
            roi_gray = gray[y_roi:y_roi+h_roi, x_roi:x_roi+w_roi]
            roi_gray = cv2.equalizeHist(roi_gray)
            _, roi_thresh = cv2.threshold(roi_gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # 转为正方形
            roi_h, roi_w = roi_thresh.shape
            max_dim = max(roi_h, roi_w)
            square_roi = np.zeros((max_dim, max_dim), dtype=np.uint8)
            y_off = (max_dim - roi_h) // 2
            x_off = (max_dim - roi_w) // 2
            square_roi[y_off:y_off+roi_h, x_off:x_off+roi_w] = roi_thresh
            
            # 调整为28x28
            resized = cv2.resize(square_roi, (28, 28), interpolation=cv2.INTER_AREA)
            normalized = resized / 255.0 - train_mean
            
            current_digits.append(normalized)
            current_rois.append(cv2.resize(square_roi, (100, 100)))
        
        processed_images.append(padded_img)
        
        if current_digits:
            images.append(current_digits)
            digit_rois.append(current_rois)
            filenames.append(filename)
            print(f"成功处理 {filename}")

    return images, original_images, processed_images, digit_rois, filenames

def predict_digits(model, images):
    """预测数字"""
    all_probabilities = []
    all_predictions = []
    all_confidences = []

    for digits in images:
        if not digits:
            all_probabilities.append([])
            all_predictions.append([])
            all_confidences.append([])
            continue

        input_data = np.array(digits).reshape(-1, 28, 28, 1)
        logits = model.predict(input_data, verbose=0)

        # Softmax
        def softmax(x):
            x_max = np.max(x, axis=1, keepdims=True)
            e_x = np.exp(x - x_max)
            return e_x / e_x.sum(axis=1, keepdims=True)

        probabilities = softmax(logits)
        predictions = np.argmax(probabilities, axis=1)
        confidences = np.max(probabilities, axis=1) * 100

        all_probabilities.append(probabilities)
        all_predictions.append(predictions)
        all_confidences.append(confidences)

    return all_probabilities, all_predictions, all_confidences

def show_results(original_images, processed_images, digit_rois, filenames,
                predictions, confidences, probabilities):
    """显示结果"""
    for i in range(len(filenames)):
        print(f"\n===== {filenames[i]} =====")

        # 显示图片
        plt.figure(figsize=(15, 8))

        plt.subplot(2, 1, 1)
        plt.imshow(cv2.cvtColor(original_images[i], cv2.COLOR_BGR2RGB))
        plt.title(f"原图: {filenames[i]}")
        plt.axis('off')

        plt.subplot(2, 1, 2)
        plt.imshow(cv2.cvtColor(processed_images[i], cv2.COLOR_BGR2RGB))
        plt.title(f"检测结果: {len(predictions[i])} 个数字")
        plt.axis('off')

        plt.tight_layout()
        plt.show()

        # 显示识别结果
        if len(predictions[i]) > 0:
            result = ''.join(map(str, predictions[i]))
            avg_conf = np.mean(confidences[i])
            print(f"识别结果: {result} (平均置信度: {avg_conf:.1f}%)")

            # 显示每个数字
            fig, axes = plt.subplots(1, len(predictions[i]), figsize=(3*len(predictions[i]), 4))
            if len(predictions[i]) == 1:
                axes = [axes]

            for j, ax in enumerate(axes):
                ax.imshow(digit_rois[i][j], cmap='gray')
                ax.set_title(f"{predictions[i][j]} ({confidences[i][j]:.1f}%)")
                ax.axis('off')

            plt.tight_layout()
            plt.show()

            # 详细分析
            for j in range(len(predictions[i])):
                print(f"数字{j+1}: {predictions[i][j]} (置信度: {confidences[i][j]:.1f}%)")
                top3 = np.argsort(probabilities[i][j])[-3:][::-1]
                for k, digit in enumerate(top3):
                    prob = probabilities[i][j][digit] * 100
                    print(f"  {k+1}. {digit}: {prob:.1f}%")
        else:
            print("未检测到数字")

        print("-" * 50)

def main():
    """主程序"""
    print("🚀 启动最终优化版数字识别系统")
    print("📚 准备模型...")

    model, train_mean = create_final_model()

    print("📁 处理图片...")
    images, original_images, processed_images, digit_rois, filenames = process_images("numbers", train_mean)

    if len(images) > 0:
        print("🔍 开始识别...")
        probabilities, predicted_digits, confidence = predict_digits(model, images)

        print("📊 显示结果...")
        show_results(original_images, processed_images, digit_rois, filenames,
                    predicted_digits, confidence, probabilities)
    else:
        print("❌ 未找到图片")

if __name__ == "__main__":
    main()
